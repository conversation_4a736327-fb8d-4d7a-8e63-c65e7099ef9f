#!/usr/bin/env python3
"""
测试翻译服务的流式输出格式是否符合OpenAI标准
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.translation_service import TranslationRequest, LLMTranslationClient


async def test_stream_format():
    """测试流式翻译输出格式"""
    print("=== 测试流式翻译输出格式 ===")
    
    # 创建测试请求
    request = TranslationRequest(
        question=[
            {"text": "Hello world"},
            {"text": "How are you?"}
        ],
        stream=True,
        translate_options={
            "src_lang": "en",
            "tgt_lang": "zh",
            "provider": "llm_translate"
        }
    )
    
    # 创建LLM翻译客户端
    client = LLMTranslationClient()
    
    print("开始流式翻译...")
    chunk_count = 0
    
    try:
        async for chunk in client.stream_translate(request):
            chunk_count += 1
            print(f"\n--- Chunk {chunk_count} ---")
            print(chunk)
            
            # 解析并验证格式
            if chunk.startswith("data: "):
                data_part = chunk[6:].strip()
                if data_part == "[DONE]":
                    print("收到完成信号")
                    continue
                    
                try:
                    parsed = json.loads(data_part)
                    print("解析后的JSON:")
                    print(json.dumps(parsed, indent=2, ensure_ascii=False))
                    
                    # 验证OpenAI格式
                    if "choices" in parsed:
                        print("✓ 包含choices字段")
                        if parsed["choices"] and "delta" in parsed["choices"][0]:
                            delta = parsed["choices"][0]["delta"]
                            print(f"✓ delta字段: {list(delta.keys())}")
                            if "text" in delta and "content" in delta and "index" in delta:
                                print("✓ delta包含必需字段: text, content, index")
                            else:
                                print("✗ delta缺少必需字段")
                        else:
                            print("✗ choices[0]缺少delta字段")
                    else:
                        print("✗ 缺少choices字段")
                        
                    # 验证其他OpenAI标准字段
                    required_fields = ["created", "id", "model", "service_tier", "object", "usage"]
                    for field in required_fields:
                        if field in parsed:
                            print(f"✓ 包含{field}字段: {parsed[field]}")
                        else:
                            print(f"✗ 缺少{field}字段")
                            
                except json.JSONDecodeError as e:
                    print(f"✗ JSON解析失败: {e}")
                    
            # 限制输出数量，避免过多输出
            if chunk_count >= 10:
                print("\n已输出10个chunk，停止测试...")
                break
                
    except Exception as e:
        print(f"测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n=== 测试完成，共处理 {chunk_count} 个chunk ===")


if __name__ == "__main__":
    asyncio.run(test_stream_format())
